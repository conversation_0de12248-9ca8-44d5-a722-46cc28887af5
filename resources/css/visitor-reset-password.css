/* Visitor Reset Password Styles */

/* Main container */
.reset-password-container {
    width: 100%;
    height: 100%;
    padding: 45px 32px;
    display: inline-flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    gap: 16px;
}

/* Header container */
.reset-header-container {
    width: 693px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
}

/* Header padding */
.reset-header-padding {
    padding: 10px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

/* Title styling */
.reset-title-blue {
    color: #007DB6;
    font-size: 24px;
    font-family: 'Gotham Narrow';
    font-weight: 400;
    text-transform: uppercase;
    word-wrap: break-word;
}

.reset-title-dark {
    color: #102649;
    font-size: 24px;
    font-family: 'Gotham Narrow';
    font-weight: 400;
    text-transform: uppercase;
    word-wrap: break-word;
}

/* Subtitle container */
.reset-subtitle-container {
    align-self: stretch;
    padding: 10px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

/* Subtitle text */
.reset-subtitle-text {
    color: #102649;
    font-size: 16px;
    font-family: 'Gotham Narrow';
    font-weight: 325;
    word-wrap: break-word;
}

/* Form container */
.reset-form-container {
    padding: 40px 56px;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    overflow: hidden;
    border-radius: 24px;
    outline: 1px #F8F9FA solid;
    outline-offset: -1px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

/* Form inner container */
.reset-form-inner {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 18px;
}

/* Form field container */
.reset-form-field-container {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 8px;
}

/* Form field */
.reset-form-field {
    width: 528px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 4px;
}

/* Form label container */
.reset-form-label-container {
    align-self: stretch;
    height: 27px;
    position: relative;
}

/* Form label */
.reset-form-label {
    position: absolute;
    left: 0px;
    top: 0px;
    color: #102649;
    font-size: 16px;
    font-family: 'Gotham Narrow';
    font-weight: 350;
    word-wrap: break-word;
}

/* Input container */
.reset-input-container {
    align-self: stretch;
    height: 56px;
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    outline: 1px rgba(16, 38, 73, 0.40) solid;
    outline-offset: -1px;
}

/* Input placeholder */
.reset-input-placeholder {
    position: absolute;
    left: 24px;
    top: 15px;
    display: inline-flex;
    justify-content: flex-start;
    align-items: center;
}

/* Placeholder text */
.reset-placeholder-text {
    color: rgba(102, 102, 102, 0.60);
    font-size: 16px;
    font-family: 'Poppins';
    font-weight: 400;
    word-wrap: break-word;
}

/* Button container */
.reset-button-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 8px;
}

/* Button */
.reset-button {
    width: 528px;
    height: 64px;
    position: relative;
    background: #102649;
    overflow: hidden;
    border-radius: 4px;
}

/* Button text container */
.reset-button-text-container {
    position: absolute;
    left: 187.50px;
    top: 18.50px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
}

/* Button text */
.reset-button-text {
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    color: white;
    font-size: 22px;
    font-family: 'Gotham Narrow';
    font-weight: 400;
    word-wrap: break-word;
}

/* Back to login container */
.reset-back-container {
    width: 528px;
    height: 24px;
    position: relative;
}

/* Back to login link container */
.reset-back-link-container {
    position: absolute;
    left: 203px;
    top: 0.50px;
    display: inline-flex;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
}

/* Back arrow icon */
.reset-back-arrow {
    width: 15px;
    height: 15px;
    position: relative;
    transform: rotate(180deg);
    transform-origin: top left;
}

/* Back arrow icon background */
.reset-back-arrow-bg {
    width: 14px;
    height: 10.99px;
    position: absolute;
    left: 0.50px;
    top: 2px;
    background: #007DB6;
}

/* Back to login text */
.reset-back-text {
    text-align: right;
    color: #007DB6;
    font-size: 16px;
    font-family: 'Gotham Narrow';
    font-weight: 400;
    word-wrap: break-word;
}
