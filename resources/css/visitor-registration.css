:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --light-color: #f8f9fa;
    --border-radius: 0.5rem;
    --completed-color: #f7a600; /* Bright yellow/orange for completed steps */
    --current-color: #1e3a8a; /* Dark navy blue for current step */
    --inactive-color: #e2e8f0; /* Light gray for inactive steps */
}

/* Progress Steps */
.progress-container {
    margin-bottom: 2rem;
}

.progress-step {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Completed step styling - yellow/orange */
.progress-step.completed .progress-circle {
    background-color: var(--completed-color);
    color: white;
    border-color: var(--completed-color);
}

/* Current step styling - dark blue */
.progress-step.current .progress-circle {
    background-color: var(--current-color);
    color: white;
    border-color: var(--current-color);
}

.progress-circle {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: var(--inactive-color);
    border: 2px solid var(--inactive-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    color: #666;
}

.progress-line {
    flex: 1;
    height: 3px;
    background-color: var(--inactive-color);
    margin: 0 1rem;
    position: relative;
    top: 1.25rem;
    transition: background-color 0.3s ease;
}

/* Completed line styling - yellow/orange */
.progress-line.completed {
    background-color: var(--completed-color);
}

/* Line before current step - dark blue */
.progress-line.current-before {
    background-color: var(--current-color);
}

.progress-text {
    font-size: 0.875rem;
    color: #666;
    white-space: nowrap;
}

/* Text color for completed and current steps */
.progress-step.completed .progress-text {
    color: var(--completed-color);
    font-weight: 500;
}

.progress-step.current .progress-text {
    color: var(--current-color);
    font-weight: 500;
}

/* Form Styles */
.form-control, .form-select {
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    border: 1px solid #9ca3af;
    transition: all 0.3s ease;
    background-color: #ffffff;
    height: auto;
    min-height: 48px;
    line-height: 1.5;
}

/* Consistent height for all input fields */
.form-control.py-3, .form-select.py-3 {
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important;
    height: 56px;
    line-height: 1.5;
}

/* Ensure rounded inputs maintain consistent height */
.form-control.rounded-3, .form-select.rounded-3 {
    height: 56px;
    padding: 0.75rem 1rem;
    line-height: 1.5;
}

/* Override any conflicting Bootstrap height rules */
.form-control.py-3.rounded-3, .form-select.py-3.rounded-3 {
    height: 56px !important;
    padding: 0.75rem 1rem !important;
    line-height: 1.5 !important;
}

/* File input specific styling */
.form-control[type="file"] {
    height: 56px;
    min-height: 56px;
    padding: 0.75rem 1rem;
    line-height: 1.5;
    border: 1px solid #9ca3af;
}

.form-control[type="file"]:hover:not(:focus) {
    border-color: #6b7280;
    box-shadow: 0 0 0 0.1rem rgba(107, 114, 128, 0.1);
}

/* Textarea specific styling */
.form-control[rows] {
    height: auto;
    min-height: 56px;
    resize: vertical;
    border: 1px solid #9ca3af;
}

.form-control[rows]:hover:not(:focus) {
    border-color: #6b7280;
    box-shadow: 0 0 0 0.1rem rgba(107, 114, 128, 0.1);
}

/* Additional override for any stubborn elements */
.visitor-registration .form-control,
.visitor-registration .form-select {
    height: 56px !important;
    min-height: 56px !important;
    box-sizing: border-box;
    border-color: #9ca3af !important;
}

/* Comprehensive gray border scheme for all input types */
input.form-control,
select.form-select,
textarea.form-control,
.form-control[type="text"],
.form-control[type="email"],
.form-control[type="password"],
.form-control[type="tel"],
.form-control[type="file"],
.form-control[type="number"] {
    border: 1px solid #9ca3af;
}

/* Hover states for all input types */
input.form-control:hover:not(:focus),
select.form-select:hover:not(:focus),
textarea.form-control:hover:not(:focus),
.form-control[type="text"]:hover:not(:focus),
.form-control[type="email"]:hover:not(:focus),
.form-control[type="password"]:hover:not(:focus),
.form-control[type="tel"]:hover:not(:focus),
.form-control[type="file"]:hover:not(:focus),
.form-control[type="number"]:hover:not(:focus) {
    border-color: #6b7280;
    box-shadow: 0 0 0 0.1rem rgba(107, 114, 128, 0.1);
}

/* Enhanced Focus States for Input Fields */
.form-control:focus,
.form-select:focus {
    border-color: #102649;
    box-shadow: 0 0 0 0.2rem rgba(16, 38, 73, 0.15);
    background-color: #ffffff;
    outline: none;
    transform: translateY(-1px);
}

/* Hover States */
.form-control:hover:not(:focus),
.form-select:hover:not(:focus) {
    border-color: #6b7280;
    box-shadow: 0 0 0 0.1rem rgba(107, 114, 128, 0.1);
}

/* Active/Focus-within States for Input Groups */
.input-group:focus-within {
    box-shadow: 0 0 0 0.2rem rgba(16, 38, 73, 0.15);
    border-radius: var(--border-radius);
}

.input-group:focus-within .form-control,
.input-group:focus-within .country-select-button {
    border-color: #102649;
}

/* Enhanced styling for rounded inputs */
.form-control.rounded-3:focus,
.form-select.rounded-3:focus {
    border-color: #102649;
    box-shadow: 0 0 0 0.25rem rgba(16, 38, 73, 0.15);
    background-color: #ffffff;
    transform: translateY(-2px);
}

/* Special focus state for password fields with toggle buttons */
.position-relative .form-control:focus {
    border-color: #102649;
    box-shadow: 0 0 0 0.2rem rgba(16, 38, 73, 0.15);
    z-index: 2;
}

/* Focus state for file inputs */
.form-control[type="file"]:focus {
    border-color: #102649;
    box-shadow: 0 0 0 0.2rem rgba(16, 38, 73, 0.15);
}

/* Focus state for checkboxes */
.form-check-input:focus {
    border-color: #102649;
    box-shadow: 0 0 0 0.2rem rgba(16, 38, 73, 0.15);
}

.form-check-input:checked {
    background-color: #102649;
    border-color: #102649;
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #102649;
    transition: color 0.3s ease;
}

/* Enhanced label styling when input is focused */
.form-control:focus + .form-label,
.form-select:focus + .form-label,
.form-control:focus ~ .form-label,
.form-select:focus ~ .form-label {
    color: #102649;
    font-weight: 600;
}

/* Label styling for required fields */
.form-label:has(+ .form-control[required]),
.form-label:has(+ .form-select[required]) {
    position: relative;
}

/* Error state styling */
.form-control.is-invalid,
.form-select.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.15);
}

.form-control.is-invalid:focus,
.form-select.is-invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.2);
}

/* Success state styling */
.form-control.is-valid,
.form-select.is-valid {
    border-color: #198754;
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.15);
}

.form-control.is-valid:focus,
.form-select.is-valid:focus {
    border-color: #198754;
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.2);
}

/* Profile Photo Upload and Cropper */
.profile-preview-container {
    width: 100px;
    height: 100px;
    overflow: hidden;
    flex-shrink: 0;
    position: relative;
}

.profile-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-placeholder {
    width: 100%;
    height: 100%;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Edit button for profile photo */
.edit-photo-btn {
    position: absolute;
    bottom: 0;
    right: 0;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    opacity: 0.9;
    transition: opacity 0.2s ease-in-out;
}

.profile-preview-container:hover .edit-photo-btn {
    opacity: 1;
}

/* Image Cropper Styles */
.img-container {
    max-height: 400px;
    width: 100%;
    margin: 0 auto;
    position: relative;
}

.img-container img {
    max-width: 100%;
    max-height: 400px;
    display: block;
    margin: 0 auto;
}

.cropper-container {
    margin: 0 auto;
}

/* Remove the circular styling for square crop area */
/* .cropper-view-box,
.cropper-face {
    border-radius: 50%;
} */

/* Password Toggle */
.password-toggle {
    cursor: pointer;
    color: var(--primary-color);
    font-size: 0.875rem;
}

/* Password toggle button styling for registration form */
.position-relative .btn[data-toggle] {
    background: none;
    border: none;
    color: #102649;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.position-relative .btn[data-toggle]:hover {
    background-color: rgba(247, 193, 0, 0.1);
    color: #F7C100;
    transform: scale(1.05);
}

.position-relative .btn[data-toggle]:focus {
    outline: 2px solid #F7C100;
    outline-offset: 2px;
    box-shadow: none;
}

.position-relative .btn[data-toggle]:active {
    transform: scale(0.95);
}

.position-relative .btn[data-toggle] i {
    font-size: 18px;
    transition: all 0.3s ease;
}

.position-relative .btn[data-toggle]:hover i {
    transform: scale(1.1);
}

/* Review Section */
.review-item {
    padding: 1rem 0;
    border-bottom: 1px solid #dee2e6;
}

.review-item:last-child {
    border-bottom: none;
}

.review-label {
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 0.25rem;
}

/* Ensure modal and backdrop are always on top */
.modal-backdrop {
    z-index: 1050 !important;
}
.modal {
    z-index: 1100 !important;
}

/* Country dropdown styling with Alpine.js */
.country-dropdown-container {
    position: relative;
}

.country-select-button {
    display: flex;
    align-items: center;
    height: 56px;
    min-height: 56px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    background-color: #f8f9fa;
    border: 1px solid #9ca3af;
    border-right: 0;
    padding: 0.75rem;
    line-height: 1.5;
    transition: all 0.3s ease;
}

.country-select-button:hover:not(:focus) {
    background-color: #e9ecef;
    border-color: #6b7280;
    box-shadow: 0 0 0 0.1rem rgba(107, 114, 128, 0.1);
}

/* Enhanced focus state for country dropdown button */
.country-select-button:focus {
    background-color: #f8f9fa;
    border-color: #102649;
    box-shadow: 0 0 0 0.2rem rgba(16, 38, 73, 0.15);
    outline: none;
}

/* Focus state for country search input */
.country-search-container .form-control:focus {
    border-color: #102649;
    box-shadow: 0 0 0 0.15rem rgba(16, 38, 73, 0.1);
}

.country-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    width: 300px;
    max-height: 300px;
    overflow-y: auto;
    background-color: white;
    border: 1px solid #9ca3af;
    border-radius: 0.25rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.country-search-container {
    position: sticky;
    top: 0;
    z-index: 1;
    padding: 10px;
    background-color: white;
    border-bottom: 1px solid #9ca3af;
}

.country-list {
    padding: 5px 0;
}

.country-item {
    display: flex;
    align-items: center;
    padding: 8px 15px;
    cursor: pointer;
}

.country-item:hover {
    background-color: #f8f9fa;
    border-left: 3px solid #F7C100;
    padding-left: 12px;
    transition: all 0.2s ease;
}

/* Active/selected country item */
.country-item:active,
.country-item.selected {
    background-color: rgba(16, 38, 73, 0.1);
    border-left: 3px solid #102649;
    padding-left: 12px;
    color: #102649;
    font-weight: 500;
}

/* Focus state for country items when navigating with keyboard */
.country-item:focus {
    background-color: rgba(16, 38, 73, 0.05);
    border-left: 3px solid #102649;
    padding-left: 12px;
    outline: none;
}

.fi {
    display: inline-block;
    margin-right: 5px;
}

/* Fix the input group with the custom dropdown */
.input-group > .country-dropdown-container {
    flex: 0 0 auto;
}

/* Ensure input group elements have consistent height */
.input-group .form-control {
    height: 56px;
    min-height: 56px;
}

.input-group .country-select-button {
    height: 56px;
    min-height: 56px;
}

/* Fix input group alignment */
.input-group {
    align-items: stretch;
}

.input-group > * {
    height: 56px;
    min-height: 56px;
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
    .progress-line {
        margin: 0 0.5rem;
    }

    .progress-text {
        font-size: 0.75rem;
    }

    /* Enhanced mobile focus states */
    .form-control:focus,
    .form-select:focus {
        transform: translateY(-2px);
        box-shadow: 0 0 0 0.3rem rgba(16, 38, 73, 0.2);
    }

    .form-control.rounded-3:focus,
    .form-select.rounded-3:focus {
        transform: translateY(-3px);
        box-shadow: 0 0 0 0.35rem rgba(16, 38, 73, 0.2);
    }

    /* Larger touch targets for mobile */
    .form-control,
    .form-select {
        height: 56px;
        min-height: 56px;
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 0.75rem 1rem;
    }

    .country-select-button {
        height: 56px;
        min-height: 56px;
        padding: 0.75rem;
    }

    .input-group .form-control,
    .input-group .country-select-button {
        height: 56px;
        min-height: 56px;
    }

    .country-item {
        padding: 12px 15px;
        min-height: 48px;
        display: flex;
        align-items: center;
    }
}

/* Make the cropper modal responsive */
@media (max-width: 768px) {
    .img-container {
        max-height: 300px;
    }
    
    .img-container img {
        max-height: 300px;
    }
}
