/**
 * Cairo Beacon Event - Visitor Registration
 * Main entry point for visitor registration functionality
 */

// Import components
import config from './components/config.js';
import utils from './components/utils.js';
import profilePhoto from './components/profilePhoto.js';
import passwordManager from './components/passwordManager.js';
import countryDropdown from './components/countryDropdown.js';
import modalManager from './components/modalManager.js';

// Main application object
const CairoBeaconEvent = {
    config,
    utils,
    profilePhoto,
    passwordManager,
    countryDropdown,
    modalManager,

    // Main initialization
    init() {
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize all modules
            this.modalManager.initModal();
            this.profilePhoto.init();
            this.passwordManager.init();
            
            // Initialize cropper if needed
            if (typeof Cropper === 'undefined') {
                this.utils.loadScript(
                    'https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.js', 
                    () => this.profilePhoto.initCropper()
                );
            } else {
                this.profilePhoto.initCropper();
            }
        });
        
        // Set up Livewire event listeners
        document.addEventListener('livewire:initialized', () => {
            // Re-initialize password manager for Livewire components
            this.passwordManager.initializeToggles();
        });

        // Handle Livewire navigation and updates
        document.addEventListener('livewire:navigated', () => {
            this.passwordManager.initializeToggles();
        });

        document.addEventListener('livewire:updated', () => {
            this.passwordManager.initializeToggles();
        });
    }
};

// Make Alpine.js components available globally
window.countryDropdown = CairoBeaconEvent.countryDropdown.create;

// Make necessary functions available globally for HTML onclick attributes
window.showImageCropper = CairoBeaconEvent.profilePhoto.showImageCropper.bind(CairoBeaconEvent.profilePhoto);
window.removeProfilePhoto = CairoBeaconEvent.profilePhoto.removeProfilePhoto.bind(CairoBeaconEvent.profilePhoto);
window.updateProfilePreview = CairoBeaconEvent.profilePhoto.updateProfilePreview.bind(CairoBeaconEvent.profilePhoto);
window.togglePasswordVisibility = CairoBeaconEvent.passwordManager.toggleVisibility.bind(CairoBeaconEvent.passwordManager);

// Initialize the application
CairoBeaconEvent.init();

// Export for use in other files if needed
export default CairoBeaconEvent;
