/**
 * Country dropdown functionality
 */
import config from './config.js';

const countryDropdown = {
    /**
     * Create Alpine.js component for country dropdown
     * @returns {Object} - Alpine.js component definition
     */
    create() {
        return {
            isOpen: false,
            searchQuery: '',
            selectedCountryCode: '',
            selectedCallingCode: '',
            
            /**
             * Initialize country dropdown with default values
             * @param {string} countryCode - Initial country code
             * @param {string} callingCode - Initial calling code
             */
            initCountryDropdown(countryCode, callingCode) {
                this.selectedCountryCode = countryCode || config.defaults.defaultCountryCode;
                this.selectedCallingCode = callingCode || config.defaults.defaultCallingCode;
                console.log('Initialized with:', this.selectedCountryCode, this.selectedCallingCode);
                
                // Set up Livewire event listener
                document.addEventListener('livewire:initialized', () => {
                    // Listen for country updates from Livewire
                    Livewire.on('country-updated', data => {
                        console.log('Received country-updated event:', data);
                        this.updateSelectedCountry(data.countryCode, data.callingCode);
                    });
                });
            },
            
            /**
             * Update selected country from Livewire events
             * @param {string} countryCode - Country code
             * @param {string} callingCode - Calling code
             */
            updateSelectedCountry(countryCode, callingCode) {
                console.log('Updating country to:', countryCode, callingCode);
                
                if (countryCode) {
                    this.selectedCountryCode = countryCode.toLowerCase();
                }
                if (callingCode) {
                    this.selectedCallingCode = callingCode;
                }
            },
            
            /**
             * Toggle dropdown visibility
             */
            toggleDropdown() {
                this.isOpen = !this.isOpen;
                if (this.isOpen) {
                    this.$nextTick(() => {
                        if (this.$refs.searchInput) {
                            this.$refs.searchInput.focus();
                        }
                    });
                }
            },
            
            /**
             * Select a country from the dropdown
             * @param {string} code - Country code
             * @param {string} callingCode - Calling code
             */
            selectCountry(code, callingCode) {
                console.log('Country selected:', code, callingCode);
                
                const countryInput = document.getElementById(
                    config.selectors.countryHiddenInput.substring(1)
                );
                
                if (countryInput) {
                    // Store the country code in uppercase as expected by the backend
                    const uppercaseCode = code.toUpperCase();
                    countryInput.value = uppercaseCode;
                    
                    // Update local state for Alpine.js reactivity
                    this.selectedCountryCode = code.toLowerCase();
                    this.selectedCallingCode = callingCode;
                    
                    // Trigger Livewire update
                    countryInput.dispatchEvent(new Event('input', { bubbles: true }));
                    
                    console.log('Updated hidden input with:', uppercaseCode);
                    console.log('Updated Alpine.js state:', this.selectedCountryCode, this.selectedCallingCode);
                } else {
                    console.error('Country input element not found!');
                }
                
                // Close the dropdown
                this.isOpen = false;
            },
            
            /**
             * Check if a country should be visible based on search query
             * @param {string} name - Country name
             * @param {string} code - Country code
             * @param {string} callingCode - Calling code
             * @returns {boolean} - Whether the country should be visible
             */
            isVisible(name, code, callingCode) {
                if (!this.searchQuery) return true;
                
                try {
                    const query = this.searchQuery.toLowerCase();
                    return (
                        name.toLowerCase().includes(query) ||
                        code.toLowerCase().includes(query) ||
                        callingCode.includes(query)
                    );
                } catch (error) {
                    console.error('Error in isVisible function:', error);
                    return true; // Default to showing the country if there's an error
                }
            },
            
            /**
             * Filter countries based on search query
             * (The filtering is handled directly in the template with x-show)
             */
            filterCountries() {}
        };
    }
};

export default countryDropdown;
