/**
 * Password visibility toggle functionality
 */
import config from './config.js';

const passwordManager = {
    // Store password visibility states to prevent conflicts
    passwordStates: new Map(),
    // Debounce mechanism to prevent rapid toggling
    toggleTimeouts: new Map(),

    /**
     * Toggle password visibility
     * @param {string} inputId - ID of password input
     */
    toggleVisibility(inputId) {
        // Clear any existing timeout for this input
        if (this.toggleTimeouts.has(inputId)) {
            clearTimeout(this.toggleTimeouts.get(inputId));
        }

        // Set a small timeout to debounce rapid clicks
        const timeoutId = setTimeout(() => {
            this.performToggle(inputId);
            this.toggleTimeouts.delete(inputId);
        }, 50);

        this.toggleTimeouts.set(inputId, timeoutId);
    },

    /**
     * Perform the actual toggle operation
     * @param {string} inputId - ID of password input
     */
    performToggle(inputId) {
        const input = document.getElementById(inputId);
        const toggle = document.querySelector(`[data-toggle="${inputId}"]`);

        if (!input || !toggle) {
            console.warn(`Password toggle elements not found for input: ${inputId}`);
            return;
        }

        const icon = toggle.querySelector('i');
        if (!icon) {
            console.warn(`Icon not found in toggle button for input: ${inputId}`);
            return;
        }

        // Get current state from our store or determine from input type
        let isCurrentlyPassword = this.passwordStates.get(inputId);
        if (isCurrentlyPassword === undefined) {
            isCurrentlyPassword = input.type === 'password';
        }

        // Toggle the state
        const newIsPassword = !isCurrentlyPassword;

        // Update input type
        input.type = newIsPassword ? 'password' : 'text';

        // Update icon
        if (newIsPassword) {
            icon.classList.remove('bi-eye');
            icon.classList.add('bi-eye-slash');
        } else {
            icon.classList.remove('bi-eye-slash');
            icon.classList.add('bi-eye');
        }

        // Store the new state
        this.passwordStates.set(inputId, newIsPassword);

        // Add a data attribute to track state on the element itself
        input.setAttribute('data-password-visible', newIsPassword ? 'false' : 'true');
    },

    /**
     * Initialize password toggles
     */
    init() {
        this.initializeToggles();

        // Re-initialize when Livewire updates the DOM
        if (window.Livewire) {
            document.addEventListener('livewire:navigated', () => {
                this.initializeToggles();
            });

            document.addEventListener('livewire:updated', () => {
                this.initializeToggles();
            });
        }
    },

    /**
     * Initialize password toggle event listeners
     */
    initializeToggles() {
        const toggles = document.querySelectorAll(config.selectors.passwordToggle);

        toggles.forEach(toggle => {
            const inputId = toggle.getAttribute('data-toggle');

            if (inputId && !toggle.hasAttribute('data-password-initialized')) {
                // Use multiple event types to ensure reliable handling
                ['click', 'mousedown'].forEach(eventType => {
                    toggle.addEventListener(eventType, (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        if (eventType === 'click') {
                            this.toggleVisibility(inputId);
                        }
                    }, { passive: false });
                });

                toggle.setAttribute('data-password-initialized', 'true');

                // Restore previous state if it exists
                this.restorePasswordState(inputId);
            }
        });
    },

    /**
     * Restore password state after DOM updates
     * @param {string} inputId - ID of password input
     */
    restorePasswordState(inputId) {
        const input = document.getElementById(inputId);
        const toggle = document.querySelector(`[data-toggle="${inputId}"]`);

        if (!input || !toggle) return;

        const storedState = this.passwordStates.get(inputId);
        const dataAttribute = input.getAttribute('data-password-visible');

        if (storedState !== undefined || dataAttribute) {
            const shouldBePassword = storedState !== undefined ? storedState : dataAttribute === 'false';
            const icon = toggle.querySelector('i');

            if (icon) {
                input.type = shouldBePassword ? 'password' : 'text';

                if (shouldBePassword) {
                    icon.classList.remove('bi-eye');
                    icon.classList.add('bi-eye-slash');
                } else {
                    icon.classList.remove('bi-eye-slash');
                    icon.classList.add('bi-eye');
                }

                this.passwordStates.set(inputId, shouldBePassword);
            }
        }
    }
};

export default passwordManager;
