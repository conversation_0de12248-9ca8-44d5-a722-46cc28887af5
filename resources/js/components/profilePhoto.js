/**
 * Profile photo handling functionality
 */
import config from './config.js';
import utils from './utils.js';

const profilePhoto = {
    cropper: null,
    croppedImageFile: null,
    croppedImageDataUrl: null,

    /**
     * Initialize profile photo functionality
     */
    init() {
        const profilePhotoInput = utils.getElement(
            config.selectors.profilePhotoInput
        );

        if (profilePhotoInput) {
            profilePhotoInput.addEventListener('change', this.showImageCropper.bind(this));
        }
    },

    /**
     * Show image cropper when photo is selected
     * @param {Event} event - Change event from file input
     */
    showImageCropper(event) {
        if (!event.target.files || !event.target.files[0]) return;

        const file = event.target.files[0];
        const reader = new FileReader();

        reader.onload = (e) => {
            const image = utils.getElement(
                config.selectors.imageToCrop
            );

            if (!image) return;

            // Set image source and clear previous cropped data
            image.src = e.target.result;
            this.croppedImageDataUrl = null;
            this.croppedImageFile = null;

            const modalElement = utils.getElement(
                config.selectors.imageCropperModal
            );

            if (modalElement) {
                const modal = new bootstrap.Modal(modalElement);
                modal.show();

                // Pre-load the image before initializing cropper
                image.onload = () => {
                    console.log('Image loaded with dimensions:', image.width, 'x', image.height);
                };
            }
        };

        reader.readAsDataURL(file);
    },

    /**
     * Remove profile photo
     */
    removeProfilePhoto() {
        const previewContainer = utils.getElement(
            config.selectors.profilePreviewContainer
        );

        if (!previewContainer) return;

        previewContainer.innerHTML = `
            <div class="profile-placeholder d-flex align-items-center justify-content-center"
                 id="profilePlaceholder">
                <i class="bi bi-person-fill fs-1 text-muted"></i>
            </div>
        `;

        const fileInput = utils.getElement(
            config.selectors.profilePhotoInput
        );

        if (fileInput) fileInput.value = '';

        this.croppedImageDataUrl = null;
        this.croppedImageFile = null;

        const livewireComponent = utils.getLivewireComponent();
        if (livewireComponent) {
            livewireComponent.set('profilePhoto', null);
        }
    },

    /**
     * Update profile preview with cropped image
     * @param {string} imageSrc - Image data URL
     */
    updateProfilePreview(imageSrc) {
        const previewContainer = utils.getElement(
            config.selectors.profilePreviewContainer
        );

        if (!previewContainer) return;

        const previewHTML = `
            <img src="${imageSrc}" alt="Profile Preview" class="img-thumbnail profile-preview" id="profilePreviewImage">
            <button type="button" class="btn btn-sm btn-danger edit-photo-btn" onclick="window.removeProfilePhoto()">
                <i class="bi bi-trash"></i>
            </button>
        `;

        previewContainer.innerHTML = previewHTML;
    },

    /**
     * Initialize the image cropper
     */
    initCropper() {
        const modal = utils.getElement(
            config.selectors.imageCropperModal
        );

        const cropButton = utils.getElement(
            config.selectors.cropButton
        );

        if (!modal || !cropButton) return;

        modal.addEventListener('shown.bs.modal', () => {
            const image = utils.getElement(
                config.selectors.imageToCrop
            );

            if (!image) return;

            try {
                setTimeout(() => {
                    if (!window.Cropper) {
                        console.error('Cropper.js library not loaded. Make sure it is included in your layout.');
                        return;
                    }

                    // Make sure the image is visible and has dimensions
                    if (image.naturalWidth === 0 || image.naturalHeight === 0) {
                        console.error('Image not fully loaded - no dimensions available');
                        return;
                    }

                    try {
                        // Destroy previous instance if it exists
                        if (this.cropper) {
                            this.cropper.destroy();
                            this.cropper = null;
                        }

                        // Create new cropper with explicit dimensions
                        const cropperOptions = {
                            ...config.defaults.cropperOptions,
                            minContainerWidth: 300,
                            minContainerHeight: 300,
                            minCropBoxWidth: 100,
                            minCropBoxHeight: 100
                        };

                        this.cropper = new Cropper(image, cropperOptions);
                        console.log('Cropper initialized successfully');
                    } catch (error) {
                        console.error('Error initializing cropper:', error);
                    }
                }, 300); // Increased timeout to ensure image is fully loaded
            } catch (error) {
                console.error('Error in cropper setup:', error);
            }
        });

        modal.addEventListener('hidden.bs.modal', () => {
            if (this.cropper) {
                this.cropper.destroy();
                this.cropper = null;
            }

            const fileInput = utils.getElement(
                config.selectors.profilePhotoInput
            );

            if (fileInput) {
                setTimeout(() => { fileInput.focus(); }, 100);
            }
        });

        cropButton.addEventListener('click', () => {
            if (!this.cropper) return;

            // Disable the crop button to prevent multiple clicks
            cropButton.disabled = true;
            cropButton.innerHTML = '<i class="bi bi-hourglass-split"></i> Processing...';

            try {
                const canvas = this.cropper.getCroppedCanvas(
                    config.defaults.croppedImageSettings
                );

                if (!canvas) {
                    console.error('Failed to create cropped canvas');
                    this.resetCropButton(cropButton);
                    return;
                }

                canvas.toBlob((blob) => {
                    this.croppedImageFile = new File([blob], 'cropped-profile-photo.png', { type: 'image/png' });

                    const reader = new FileReader();
                    reader.onloadend = () => {
                        this.croppedImageDataUrl = reader.result;
                        this.updateProfilePreview(this.croppedImageDataUrl);
                    };

                    reader.readAsDataURL(blob);

                    // Close modal immediately to prevent UI blocking
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }

                    // Start the upload after modal is closed
                    setTimeout(() => {
                        const livewireComponent = utils.getLivewireComponent();
                        if (livewireComponent) {
                            this.showUploadProgress();
                            livewireComponent.upload('profilePhoto', this.croppedImageFile);
                        }
                    }, 300); // Small delay to ensure modal is fully closed

                }, 'image/png');
            } catch (error) {
                console.error('Error during image cropping:', error);
                this.resetCropButton(cropButton);
            }
        });
    },

    /**
     * Reset crop button to original state
     * @param {HTMLElement} cropButton - The crop button element
     */
    resetCropButton(cropButton) {
        if (cropButton) {
            cropButton.disabled = false;
            cropButton.innerHTML = 'Crop & Save';
        }
    },

    /**
     * Show upload progress indicator
     */
    showUploadProgress() {
        const previewContainer = utils.getElement(
            config.selectors.profilePreviewContainer
        );

        if (previewContainer) {
            const progressHTML = `
                <div class="d-flex align-items-center justify-content-center" style="width: 100px; height: 100px; background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 0.375rem;">
                    <div class="text-center">
                        <div class="spinner-border spinner-border-sm text-primary mb-2" role="status">
                            <span class="visually-hidden">Uploading...</span>
                        </div>
                        <div class="small text-muted">Uploading...</div>
                    </div>
                </div>
            `;
            previewContainer.innerHTML = progressHTML;
        }
    },

    /**
     * Initialize upload event listeners
     */
    initUploadListeners() {
        // Listen for Livewire upload events
        document.addEventListener('livewire:upload-start', () => {
            console.log('Upload started');
        });

        document.addEventListener('livewire:upload-finish', () => {
            console.log('Upload finished');
            this.hideUploadProgress();
        });

        document.addEventListener('livewire:upload-error', () => {
            console.error('Upload failed');
            this.hideUploadProgress();
            this.showUploadError();
        });
    },

    /**
     * Hide upload progress indicator
     */
    hideUploadProgress() {
        // The preview will be updated by Livewire automatically
        console.log('Upload progress hidden');
    },

    /**
     * Show upload error message
     */
    showUploadError() {
        const previewContainer = utils.getElement(
            config.selectors.profilePreviewContainer
        );

        if (previewContainer) {
            const errorHTML = `
                <div class="d-flex align-items-center justify-content-center" style="width: 100px; height: 100px; background-color: #f8f9fa; border: 1px solid #dc3545; border-radius: 0.375rem;">
                    <div class="text-center">
                        <i class="bi bi-exclamation-triangle text-danger fs-3 mb-1"></i>
                        <div class="small text-danger">Upload failed</div>
                    </div>
                </div>
            `;
            previewContainer.innerHTML = errorHTML;
        }
    }
};

export default profilePhoto;
