/**
 * Profile photo handling functionality
 */
import config from './config.js';
import utils from './utils.js';

const profilePhoto = {
    cropper: null,
    croppedImageFile: null,
    croppedImageDataUrl: null,

    /**
     * Initialize profile photo functionality
     */
    init() {
        const profilePhotoInput = utils.getElement(
            config.selectors.profilePhotoInput
        );
        
        if (profilePhotoInput) {
            profilePhotoInput.addEventListener('change', this.showImageCropper.bind(this));
        }
    },

    /**
     * Show image cropper when photo is selected
     * @param {Event} event - Change event from file input
     */
    showImageCropper(event) {
        if (!event.target.files || !event.target.files[0]) return;
        
        const file = event.target.files[0];
        const reader = new FileReader();
        
        reader.onload = (e) => {
            const image = utils.getElement(
                config.selectors.imageToCrop
            );
            
            if (!image) return;
            
            // Set image source and clear previous cropped data
            image.src = e.target.result;
            this.croppedImageDataUrl = null;
            this.croppedImageFile = null;
            
            const modalElement = utils.getElement(
                config.selectors.imageCropperModal
            );
            
            if (modalElement) {
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
                
                // Pre-load the image before initializing cropper
                image.onload = () => {
                    console.log('Image loaded with dimensions:', image.width, 'x', image.height);
                };
            }
        };
        
        reader.readAsDataURL(file);
    },

    /**
     * Remove profile photo
     */
    removeProfilePhoto() {
        const previewContainer = utils.getElement(
            config.selectors.profilePreviewContainer
        );
        
        if (!previewContainer) return;
        
        previewContainer.innerHTML = `
            <div class="profile-placeholder d-flex align-items-center justify-content-center" 
                 id="profilePlaceholder">
                <i class="bi bi-person-fill fs-1 text-muted"></i>
            </div>
        `;
        
        const fileInput = utils.getElement(
            config.selectors.profilePhotoInput
        );
        
        if (fileInput) fileInput.value = '';
        
        this.croppedImageDataUrl = null;
        this.croppedImageFile = null;
        
        const livewireComponent = utils.getLivewireComponent();
        if (livewireComponent) {
            livewireComponent.set('profilePhoto', null);
        }
    },

    /**
     * Update profile preview with cropped image
     * @param {string} imageSrc - Image data URL
     */
    updateProfilePreview(imageSrc) {
        const previewContainer = utils.getElement(
            config.selectors.profilePreviewContainer
        );
        
        if (!previewContainer) return;
        
        const previewHTML = `
            <img src="${imageSrc}" alt="Profile Preview" class="img-thumbnail profile-preview" id="profilePreviewImage">
            <button type="button" class="btn btn-sm btn-danger edit-photo-btn" onclick="window.removeProfilePhoto()">
                <i class="bi bi-trash"></i>
            </button>
        `;
        
        previewContainer.innerHTML = previewHTML;
    },

    /**
     * Initialize the image cropper
     */
    initCropper() {
        const modal = utils.getElement(
            config.selectors.imageCropperModal
        );
        
        const cropButton = utils.getElement(
            config.selectors.cropButton
        );
        
        if (!modal || !cropButton) return;
        
        modal.addEventListener('shown.bs.modal', () => {
            const image = utils.getElement(
                config.selectors.imageToCrop
            );
            
            if (!image) return;
            
            try {
                setTimeout(() => {
                    if (!window.Cropper) {
                        console.error('Cropper.js library not loaded. Make sure it is included in your layout.');
                        return;
                    }
                    
                    // Make sure the image is visible and has dimensions
                    if (image.naturalWidth === 0 || image.naturalHeight === 0) {
                        console.error('Image not fully loaded - no dimensions available');
                        return;
                    }

                    try {
                        // Destroy previous instance if it exists
                        if (this.cropper) {
                            this.cropper.destroy();
                            this.cropper = null;
                        }
                        
                        // Create new cropper with explicit dimensions
                        const cropperOptions = {
                            ...config.defaults.cropperOptions,
                            minContainerWidth: 300,
                            minContainerHeight: 300,
                            minCropBoxWidth: 100,
                            minCropBoxHeight: 100
                        };
                        
                        this.cropper = new Cropper(image, cropperOptions);
                        console.log('Cropper initialized successfully');
                    } catch (error) {
                        console.error('Error initializing cropper:', error);
                    }
                }, 300); // Increased timeout to ensure image is fully loaded
            } catch (error) {
                console.error('Error in cropper setup:', error);
            }
        });
        
        modal.addEventListener('hidden.bs.modal', () => {
            if (this.cropper) {
                this.cropper.destroy();
                this.cropper = null;
            }
            
            const fileInput = utils.getElement(
                config.selectors.profilePhotoInput
            );
            
            if (fileInput) {
                // Return focus to the file input when modal is closed
                setTimeout(() => { 
                    fileInput.focus(); 
                    // Don't clear the file input value as it prevents showing the selected file name
                    // fileInput.value = '';
                }, 100);
            }
            
            // Remove focus from crop button to prevent accessibility issues
            const cropButton = utils.getElement(config.selectors.cropButton);
            if (cropButton) {
                cropButton.blur();
            }
        });
        
        cropButton.addEventListener('click', () => {
            if (!this.cropper) return;
            
            try {
                const canvas = this.cropper.getCroppedCanvas(
                    config.defaults.croppedImageSettings
                );
                
                if (!canvas) {
                    console.error('Failed to create cropped canvas');
                    return;
                }
                
                canvas.toBlob((blob) => {
                    this.croppedImageFile = new File([blob], 'cropped-profile-photo.png', { type: 'image/png' });
                    
                    const reader = new FileReader();
                    reader.onloadend = () => {
                        this.croppedImageDataUrl = reader.result;
                        this.updateProfilePreview(this.croppedImageDataUrl);
                    };
                    
                    reader.readAsDataURL(blob);
                    
                    const livewireComponent = utils.getLivewireComponent();
                    if (livewireComponent) {
                        livewireComponent.upload('profilePhoto', this.croppedImageFile);
                    }
                    
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                }, 'image/png');
            } catch (error) {
                console.error('Error during image cropping:', error);
            }
        });
    }
};

export default profilePhoto;
