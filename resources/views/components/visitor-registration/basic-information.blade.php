<div>
    <div class="row mb-3">
        <div class="col-md-6 mb-3 mb-md-0">
            <label for="firstName" class="form-label">First Name*</label>
            <input type="text" wire:model.live="firstName" id="firstName" class="form-control rounded-3 py-3" placeholder="Enter Your First Name">
            @error('firstName') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
        <div class="col-md-6">
            <label for="lastName" class="form-label">Last Name*</label>
            <input type="text" wire:model.live="lastName" id="lastName" class="form-control rounded-3 py-3" placeholder="Enter Your Last Name">
            @error('lastName') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-md-6 mb-3 mb-md-0">
            <label for="nationality" class="form-label">Nationality*</label>
            <select wire:model.live="nationality" id="nationality" class="form-select rounded-3 py-3">
                <option value="">Select Your Nationality</option>
                @foreach($countries as $countryOption)
                    <option value="{{ $countryOption['nationality'] }}">
                        {{ $countryOption['nationality'] }}
                    </option>
                @endforeach
            </select>
            @error('nationality') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
        <div class="col-md-6">
            <label for="country" class="form-label">Country*</label>
            <select wire:model.live="country" id="country" class="form-select rounded-3 py-3">
                <option value="">Select Your Country</option>
                @foreach($countries as $countryOption)
                    <option
                        value="{{ $countryOption['code'] }}"
                        data-flag="{{ strtolower($countryOption['code']) }}"
                        data-calling-code="{{ $countryOption['calling_code'] }}"
                    >
                        {{ $countryOption['name'] }}
                    </option>
                @endforeach
            </select>
            @error('country') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
    </div>

    <!-- Phone field with proper flag display -->
    <div class="row mb-3">
        <div class="col-md-6 mb-3 mb-md-0">
            <label for="email" class="form-label">Email*</label>
            <input type="email" wire:model.live="email" id="email" class="form-control rounded-3 py-3" placeholder="Enter Your Email">
            @error('email') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
        <div class="col-md-6">
            <label for="phone" class="form-label">Phone Number*</label>
            <div class="input-group rounded-3">
                <div
                    x-data="countryDropdown()"
                    x-init="initCountryDropdown('{{ strtolower($country) }}', '{{ $selectedCountryCallingCode }}')"
                    @country-updated.window="updateSelectedCountry($event.detail.countryCode, $event.detail.callingCode)"
                    class="country-dropdown-container"
                >
                    <button
                        type="button"
                        @click="toggleDropdown"
                        class="btn country-select-button d-flex align-items-center"
                    >
                        <span :class="'fi fi-' + selectedCountryCode + ' me-1'"></span>
                        <span x-text="'+' + selectedCallingCode"></span>
                        <span class="ms-1"><i class="bi bi-chevron-down"></i></span>
                    </button>

                    <div
                        x-show="isOpen"
                        @click.away="isOpen = false"
                        class="country-dropdown"
                        x-transition
                    >
                        <div class="country-search-container">
                            <input
                                type="text"
                                x-model="searchQuery"
                                @input="filterCountries"
                                placeholder="Search countries..."
                                class="form-control form-control-sm"
                                x-ref="searchInput"
                            >
                        </div>

                        <div class="country-list">
                            @foreach($countries as $countryOption)
                                <div
                                    class="country-item"
                                    x-show="isVisible('{{ $countryOption['name'] }}', '{{ $countryOption['code'] }}', '{{ $countryOption['calling_code'] }}')"
                                    @click="selectCountry('{{ $countryOption['code'] }}', '{{ $countryOption['calling_code'] }}')"
                                >
                                    <span class="fi fi-{{ strtolower($countryOption['code']) }} me-2"></span>
                                    <span>{{ $countryOption['name'] }} (+{{ $countryOption['calling_code'] }})</span>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <input type="hidden" wire:model.live="country" id="country-hidden-input">
                </div>

                <input type="tel" wire:model.live="phone" id="phone" class="form-control py-3" placeholder="Enter Your Phone Number">
            </div>
            @error('phone') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
    </div>

    <!-- Password fields -->
    <div class="row mb-4">
        <div class="col-md-6 mb-3 mb-md-0">
            <label for="password" class="form-label">Password*</label>
            <div class="position-relative">
                <input
                    type="password"
                    wire:model.defer="password"
                    id="password"
                    class="form-control rounded-3 py-3"
                    placeholder="Create Your Password"
                >
                <button
                    type="button"
                    class="btn position-absolute end-0 top-0 mt-1 me-1"
                    data-toggle="password"
                >
                    <i class="bi bi-eye-slash"></i>
                </button>
            </div>
            @error('password') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
        <div class="col-md-6">
            <label for="password_confirmation" class="form-label">Confirm Password*</label>
            <div class="position-relative">
                <input
                    type="password"
                    wire:model.defer="passwordConfirmation"
                    id="passwordConfirmation"
                    class="form-control rounded-3 py-3"
                    placeholder="Confirm Your Password"
                >
                <button
                    type="button"
                    class="btn position-absolute end-0 top-0 mt-1 me-1"
                    data-toggle="passwordConfirmation"
                >
                    <i class="bi bi-eye-slash"></i>
                </button>
            </div>
            @error('passwordConfirmation') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
    </div>

    <!-- Discount code and Whatsapp number fields -->
    <div class="row mb-3">
        <div class="col-md-6 mb-3 mb-md-0">
            <label for="discountCode" class="form-label">Discount Code</label>
            <input type="text" wire:model.live="discountCode" id="discountCode" class="form-control rounded-3 py-3" placeholder="Enter Your Discount Code">
            @error('discountCode') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
        <div class="col-md-6">
            <label for="whatsappNumber" class="form-label">Whatsapp Number</label>
            <div class="input-group rounded-3">
                <input type="tel" wire:model.live="whatsappNumber" id="whatsappNumber" class="form-control py-3 border-start-0" placeholder="Enter Your Whatsapp Number">
            </div>
            @error('whatsappNumber') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
    </div>

    <!-- File upload with image cropping -->
    <div class="mb-4">
        <div class="row mb-4">
            <div class="col-12">
                <label for="profilePhoto" class="form-label">Upload your professional photo <i class="bi bi-info-circle" data-bs-toggle="tooltip" title="Upload a professional photo for your badge"></i></label>
                <div class="d-flex align-items-center">
                    <div class="position-relative me-3" id="profilePreviewContainer" style="width: 100px; height: 100px;">
                        @if($profilePhoto)
                            <img src="{{ $profilePhoto->temporaryUrl() }}" alt="Profile Preview" class="img-thumbnail profile-preview" id="profilePreviewImage">
                            <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0 m-1" onclick="window.profilePhoto.removeProfilePhoto()">
                                <i class="bi bi-trash"></i>
                            </button>
                        @else
                            <div id="profilePlaceholder" class="border rounded d-flex align-items-center justify-content-center" style="width: 100px; height: 100px; background-color: #f8f9fa;">
                                <i class="bi bi-person fs-1 text-muted"></i>
                            </div>
                        @endif
                    </div>
                    <div class="flex-grow-1">
                        <input type="file" class="form-control" id="profilePhotoInput" accept="image/*" wire:model="profilePhoto">
                        <div class="form-text">The image must not exceed 1MB</div>
                    </div>
                </div>
                @error('profilePhoto') <div class="text-danger mt-1">{{ $message }}</div> @enderror
            </div>
        </div>

        <div class="form-check mt-3">
            <input class="form-check-input" type="checkbox" wire:model.live="photoConsent" id="photoConsent">
            <label class="form-check-label" for="photoConsent">
                By submitting your photo, you grant us the right to use them for marketing purposes, without sharing your personal information with any third party. Selfies, personal pictures, and pixelated pictures are not permitted at this event.
            </label>
        </div>
        @error('photoConsent') <span class="text-danger">{{ $message }}</span> @enderror
    </div>
</div>

<!-- Move the Image Cropper Modal to the bottom for proper stacking -->
<div class="modal fade" id="imageCropperModal" tabindex="-1" aria-labelledby="imageCropperModalLabel" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageCropperModalLabel">Crop Your Profile Photo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="img-container" style="height: 400px; max-width: 100%; overflow: hidden;">
                    <img id="imageToCrop" src="" alt="Image to crop" style="max-width: 100%; display: block;">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="cropButton">Crop & Save</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('livewire:load', function() {
        // Add a global listener for Livewire file uploads
        window.addEventListener('livewire-upload-start', function() {
            console.log('Upload started');
            // Set a timeout to ensure the page becomes interactive again
            setTimeout(function() {
                document.querySelectorAll('input, button, select, textarea').forEach(function(el) {
                    el.disabled = false;
                });
                document.querySelectorAll('[wire\\:loading]').forEach(function(el) {
                    el.style.display = 'none';
                });
                document.querySelectorAll('.opacity-50').forEach(function(el) {
                    el.classList.remove('opacity-50');
                });
            }, 5000); // 5 seconds timeout
        });
        
        // Also listen for upload finish events
        window.addEventListener('livewire-upload-finish', function() {
            console.log('Upload finished');
            // Ensure the page becomes interactive immediately
            document.querySelectorAll('input, button, select, textarea').forEach(function(el) {
                el.disabled = false;
            });
            document.querySelectorAll('[wire\\:loading]').forEach(function(el) {
                el.style.display = 'none';
            });
            document.querySelectorAll('.opacity-50').forEach(function(el) {
                el.classList.remove('opacity-50');
            });
        });
        
        // Also listen for upload error events
        window.addEventListener('livewire-upload-error', function() {
            console.log('Upload error');
            // Ensure the page becomes interactive immediately
            document.querySelectorAll('input, button, select, textarea').forEach(function(el) {
                el.disabled = false;
            });
            document.querySelectorAll('[wire\\:loading]').forEach(function(el) {
                el.style.display = 'none';
            });
            document.querySelectorAll('.opacity-50').forEach(function(el) {
                el.classList.remove('opacity-50');
            });
        });
    });
</script>
