<div>
    <div class="visitor-login-container">
        <!-- Header -->
        <div class="login-header">
            <div class="login-title-container">
                <h2 class="login-title-secondary">Welcome Back! Access Your Account or Join Us Today</h2>
            </div>
            <div class="login-subtitle-container">
                <p class="login-subtitle">Log in to continue your journey with us, or create a new account to explore
                    exciting opportunities!</p>
            </div>
        </div>

        <!-- Login/Signup Tabs -->
        <div class="tabs-container">
            <div class="row g-0" style="border: 1px solid #dee2e6; width: 628px;">
                <div class="col-6">
                    <a href="{{ route('visitor.login') }}" class="btn w-100 py-3 text-white"
                       style="background-color: #102649; border: none; border-radius: 0;">Existing User? Login</a>
                </div>
                <div class="col-6">
                    <a href="{{ route('visitor.register') }}" class="btn w-100 py-3 text-dark"
                       style="background-color: white; border: 1px solid #102649; border-radius: 0;">New User? Sign Up</a>
                </div>
            </div>
        </div>

        <div class="login-form-container {{ !empty($errorMessage) || session('error') ? 'has-error' : '' }}">
            <form wire:submit="login">
                @if(!empty($errorMessage))
                    <div class="alert alert-danger">
                        {{ $errorMessage }}
                    </div>
                @endif

                @if(!empty($successMessage))
                    <div class="alert alert-success">
                        {{ $successMessage }}
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger">
                        {{ session('error') }}
                    </div>
                @endif

                <div class="login-form-fields">
                    <div class="input-field-container {{ $errors->has('email') ? 'has-error' : '' }}" data-property-1="Generic Text field">
                        <div class="input-label-container">
                            <div class="input-label">Email Address*</div>
                        </div>
                        <div class="input-field">
                            <input type="email" wire:model.blur="email" class="form-control"
                                   placeholder="Enter your email address">
                        </div>
                        @error('email') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>

                    <div class="input-field-container {{ $errors->has('password') ? 'has-error' : '' }}" data-property-1="Password field">
                        <div class="input-label-container">
                            <div class="input-label">Password*</div>
                        </div>
                        <div class="input-field password-field-wrapper">
                            <input
                                type="password"
                                id="password-field"
                                wire:model.defer="password"
                                class="form-control"
                                placeholder="Enter your password">
                            <button type="button" class="password-toggle-btn" data-toggle="password-field">
                                <i class="bi bi-eye-slash"></i>
                            </button>
                        </div>
                    </div>
                </div>

                @error('password') <span class="text-danger">{{ $message }}</span> @enderror

                <div class="button-container">
                    <button type="submit" class="submit-button" wire:loading.attr="disabled" wire:target="login">
                        <div class="button-text-container">
                            <div class="button-text">
                                <span wire:loading.remove wire:target="login">Login</span>
                                <span wire:loading wire:target="login">Logging in...</span>
                            </div>
                        </div>
                    </button>
                </div>

                <div class="remember-me-container">
                    <div class="custom-checkbox-wrapper">
                        <input type="checkbox" wire:model.blur="remember" id="remember" class="custom-checkbox-input">
                        <label for="remember" class="custom-checkbox-label">
                            <span class="custom-checkbox-box">
                                <svg class="custom-checkbox-icon" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"/>
                                </svg>
                            </span>
                            <span class="checkbox-text">Remember my login information</span>
                        </label>
                    </div>
                </div>

                <div class="terms-container">
                    <div>
                        <span class="terms-text">By continuing, you agree to the </span>
                        <a href="#" class="terms-link">Terms of use</a>
                        <span class="terms-text"> and </span>
                        <a href="#" class="terms-link">Privacy Policy.</a>
                    </div>
                </div>

                <div class="forgot-password-container">
                    <a href="{{ route('visitor.reset-password') }}" class="forgot-password-link">Forget your password</a>
                </div>
            </form>
        </div>
    </div>
</div>