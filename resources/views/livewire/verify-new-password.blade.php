<div style="width: 100%; height: 100%; position: relative; background: white; overflow: hidden">
    <div style="width: 1280px; height: 76px; left: 0px; top: 0px; position: absolute; background: white; overflow: hidden">
        <div style="width: 1062.58px; left: 110px; top: 8px; position: absolute; justify-content: space-between; align-items: center; display: inline-flex">
            <div style="width: 125.83px; height: 60px; position: relative">
                <img style="width: 125.83px; height: 50px; left: -5px; top: 5px; position: absolute" src="https://placehold.co/126x50" />
            </div>
            <div style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div style="width: 58.95px; height: 40px; position: relative">
                    <div style="width: 43.15px; height: 24px; left: 8px; top: 8px; position: absolute; justify-content: center; display: flex; flex-direction: column; color: rgba(0, 0, 0, 0.65); font-size: 15.88px; font-family: Gotham Narrow; font-weight: 325; line-height: 24px; word-wrap: break-word">About</div>
                </div>
                <div style="width: 81.98px; height: 40px; position: relative">
                    <div style="width: 66.18px; height: 24px; left: 8px; top: 8px; position: absolute; justify-content: center; display: flex; flex-direction: column; color: rgba(0, 0, 0, 0.65); font-size: 15.38px; font-family: Gotham Narrow; font-weight: 325; line-height: 24px; word-wrap: break-word">Programs</div>
                </div>
                <div style="width: 143.78px; height: 40px; position: relative">
                    <div style="width: 127.98px; height: 24px; left: 8px; top: 8px; position: absolute; justify-content: center; display: flex; flex-direction: column; color: rgba(0, 0, 0, 0.65); font-size: 15.12px; font-family: Gotham Narrow; font-weight: 325; line-height: 24px; word-wrap: break-word">Corporate Training</div>
                </div>
                <div style="width: 66.59px; height: 40px; position: relative">
                    <div style="width: 38.72px; height: 24px; left: 8px; top: 8px; position: absolute; justify-content: center; display: flex; flex-direction: column; color: rgba(0, 0, 0, 0.65); font-size: 16px; font-family: Gotham Narrow; font-weight: 325; line-height: 24px; word-wrap: break-word">More </div>
                    <div style="width: 8px; height: 4px; left: 50.59px; top: 16.92px; position: absolute; border-left: 4px black solid; border-top: 4px black solid; border-right: 4px black solid"></div>
                </div>
                <div style="width: 81.28px; height: 40px; position: relative; background: #102649">
                    <div style="width: 55.48px; height: 24px; left: 13px; top: 7px; position: absolute; text-align: center; justify-content: center; display: flex; flex-direction: column; color: white; font-size: 14px; font-family: Inter; font-weight: 700; line-height: 24px; word-wrap: break-word">Contact</div>
                </div>
            </div>
        </div>
    </div>
    <div style="width: 1280px; height: 80px; left: 0px; top: 76px; position: absolute; background: #102649">
        <div style="width: 974px; height: 71px; left: 103px; top: 4px; position: absolute">
            <img style="width: 142px; height: 71px; left: 0px; top: 0px; position: absolute" src="https://placehold.co/142x71" />
            <div style="padding-top: 1px; left: 170px; top: 11px; position: absolute; justify-content: center; align-items: center; gap: 227px; display: inline-flex">
                <div style="justify-content: center; display: flex; flex-direction: column; color: white; font-size: 24px; font-family: Gotham Narrow; font-weight: 400; text-transform: uppercase; line-height: 48px; word-wrap: break-word">Future of Finance 2024</div>
                <div style="width: 290px; height: 48px; position: relative">
                    <div style="width: 290px; height: 24px; left: 0px; top: 0px; position: absolute"></div>
                    <div style="width: 290px; height: 24px; left: 0px; top: 24px; position: absolute">
                        <div style="left: 3px; top: -24px; position: absolute; justify-content: center; align-items: center; gap: 46px; display: inline-flex">
                            <div style="width: 30px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                                <div style="align-self: stretch; color: white; font-size: 24px; font-family: Gotham Narrow; font-weight: 400; line-height: 24px; word-wrap: break-word">06</div>
                                <div style="align-self: stretch; color: white; font-size: 11px; font-family: Gotham Narrow; font-weight: 400; line-height: 24px; word-wrap: break-word">Days</div>
                            </div>
                            <div style="width: 37px; flex-direction: column; justify-content: flex-start; align-items: center; display: inline-flex">
                                <div style="align-self: stretch; color: white; font-size: 24px; font-family: Gotham Narrow; font-weight: 400; line-height: 24px; word-wrap: break-word">21</div>
                                <div style="align-self: stretch; color: white; font-size: 12px; font-family: Gotham Narrow; font-weight: 400; line-height: 24px; word-wrap: break-word">Hours</div>
                            </div>
                            <div style="width: 38px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                                <div style="align-self: stretch; text-align: center; color: white; font-size: 24px; font-family: Gotham Narrow; font-weight: 400; line-height: 24px; word-wrap: break-word">11</div>
                                <div style="align-self: stretch; color: white; font-size: 11px; font-family: Gotham Narrow; font-weight: 400; line-height: 24px; word-wrap: break-word">Minutes</div>
                            </div>
                            <div style="width: 42px; height: 48px; position: relative">
                                <div style="width: 42px; left: 0px; top: 0px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: center; display: inline-flex">
                                    <div style="align-self: stretch; color: white; font-size: 24px; font-family: Gotham Narrow; font-weight: 400; line-height: 24px; word-wrap: break-word">58</div>
                                    <div style="align-self: stretch; color: white; font-size: 11px; font-family: Gotham Narrow; font-weight: 400; line-height: 24px; word-wrap: break-word">Seconds</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div style="padding-left: 32px; padding-right: 32px; padding-top: 45px; padding-bottom: 45px; left: 262px; top: 204px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: center; gap: 16px; display: inline-flex">
        <div style="width: 693px; flex-direction: column; justify-content: flex-start; align-items: center; display: flex">
            <div style="padding: 10px; justify-content: center; align-items: center; gap: 10px; display: inline-flex">
                <div><span style="color: #007DB6; font-size: 24px; font-family: Gotham Narrow; font-weight: 400; text-transform: uppercase; word-wrap: break-word">Forgot</span><span style="color: #102649; font-size: 24px; font-family: Gotham Narrow; font-weight: 400; text-transform: uppercase; word-wrap: break-word"> your password?</span></div>
            </div>
            <div style="align-self: stretch; padding: 10px; justify-content: center; align-items: center; gap: 10px; display: inline-flex">
                <div style="color: #102649; font-size: 16px; font-family: Gotham Narrow; font-weight: 325; word-wrap: break-word">No worries! Simply enter your email address, and follow the instructions.</div>
            </div>
        </div>
        <div style="padding-left: 56px; padding-right: 56px; padding-top: 40px; padding-bottom: 40px; box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25); overflow: hidden; border-radius: 24px; outline: 1px #F8F9FA solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; gap: 10px; display: flex">
            <div style="flex-direction: column; justify-content: center; align-items: center; gap: 18px; display: flex">
                <div style="flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 8px; display: flex">
                    <div data-property-1="Generic Text field" style="width: 528px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: flex">
                        <div style="align-self: stretch; height: 27px; position: relative">
                            <div style="left: 0px; top: 0px; position: absolute; color: #102649; font-size: 16px; font-family: Gotham Narrow; font-weight: 350; word-wrap: break-word">Email Address*</div>
                        </div>
                        <div style="align-self: stretch; height: 56px; position: relative; overflow: hidden; border-radius: 12px; outline: 1px rgba(16, 38, 73, 0.40) solid; outline-offset: -1px">
                            <div style="left: 24px; top: 15px; position: absolute; justify-content: flex-start; align-items: center; display: inline-flex">
                                <div style="color: rgba(102, 102, 102, 0.60); font-size: 16px; font-family: Poppins; font-weight: 400; word-wrap: break-word">Enter Your Email Address</div>
                            </div>
                        </div>
                    </div>
                    <div style="flex-direction: column; justify-content: center; align-items: center; gap: 8px; display: flex">
                        <div style="width: 528px; height: 64px; position: relative; background: #102649; overflow: hidden; border-radius: 4px">
                            <div style="left: 187.50px; top: 18.50px; position: absolute; justify-content: center; align-items: center; gap: 8px; display: inline-flex">
                                <div style="text-align: center; justify-content: center; display: flex; flex-direction: column; color: white; font-size: 22px; font-family: Gotham Narrow; font-weight: 400; word-wrap: break-word">Reset Password</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="width: 528px; height: 24px; position: relative">
                    <div style="left: 203px; top: 0.50px; position: absolute; justify-content: flex-start; align-items: center; gap: 10px; display: inline-flex">
                        <div style="width: 15px; height: 15px; position: relative; transform: rotate(180deg); transform-origin: top left">
                            <div style="width: 14px; height: 10.99px; left: 0.50px; top: 2px; position: absolute; background: #007DB6"></div>
                        </div>
                        <div style="text-align: right; color: #007DB6; font-size: 16px; font-family: Gotham Narrow; font-weight: 400; word-wrap: break-word">Back to Login</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div style="width: 1281px; left: -1px; top: 690px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
        <div style="align-self: stretch; height: 201px; padding-left: 73px; padding-right: 73px; padding-top: 28px; padding-bottom: 28px; background: #102649; justify-content: center; align-items: center; display: inline-flex">
            <div style="width: 531px; flex-direction: column; justify-content: center; align-items: flex-start; gap: 4px; display: inline-flex">
                <div style="color: #F7C100; font-size: 36px; font-family: Gotham Narrow; font-weight: 400; text-transform: uppercase; line-height: 48px; word-wrap: break-word">Stay updated</div>
                <div style="width: 483px; color: white; font-size: 16px; font-family: Gotham Narrow; font-weight: 350; line-height: 24px; word-wrap: break-word">Join our mailing list to receive exclusive offers and financial training resources.</div>
            </div>
            <div style="width: 536px; height: 84px; position: relative; background: #FAFAFA; overflow: hidden; border-radius: 8px">
                <div style="left: 37px; top: 18px; position: absolute; color: #B2B2B2; font-size: 16px; font-family: Plus Jakarta Sans; font-weight: 400; line-height: 48px; word-wrap: break-word">Enter email  address  </div>
                <div style="width: 209.46px; height: 56px; padding: 16px; left: 309px; top: 14px; position: absolute; background: #F7C100; border-radius: 4px; flex-direction: column; justify-content: center; align-items: center; gap: 10px; display: inline-flex">
                    <div style="justify-content: center; align-items: center; gap: 9px; display: inline-flex">
                        <div style="text-align: right; justify-content: center; display: flex; flex-direction: column; color: #102649; font-size: 18px; font-family: Plus Jakarta Sans; font-weight: 700; line-height: 24px; word-wrap: break-word">Subscribe </div>
                    </div>
                </div>
            </div>
        </div>
        <div style="align-self: stretch; height: 332px; background: #102649; flex-direction: column; justify-content: flex-start; align-items: center; display: flex">
            <div style="width: 1066px; max-width: 1320px; padding-top: 48px; padding-bottom: 48px; flex-direction: column; justify-content: flex-start; align-items: center; gap: 16px; display: flex">
                <div style="align-self: stretch; justify-content: center; align-items: flex-start; display: inline-flex; flex-wrap: wrap; align-content: flex-start">
                    <div style="flex: 1 1 0; height: 163.18px; max-width: 1320px; padding-left: 12px; padding-right: 12px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                        <div style="align-self: stretch; height: 27.19px; position: relative">
                            <div style="width: 285px; padding-bottom: 0.59px; left: 0px; top: -0.60px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                                <div style="justify-content: center; display: flex; flex-direction: column; color: white; font-size: 14.62px; font-family: Inter; font-weight: 700; line-height: 19.20px; word-wrap: break-word">Beacon FinTrain</div>
                            </div>
                        </div>
                        <div style="align-self: stretch; padding-bottom: 16px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
                            <div style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
                                <div style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
                                    <div style="justify-content: flex-start; align-items: flex-start; display: inline-flex">
                                        <div style="justify-content: center; display: flex; flex-direction: column; color: #F8F9FA; font-size: 15.38px; font-family: Inter; font-weight: 700; line-height: 24px; word-wrap: break-word">About us</div>
                                    </div>
                                </div>
                                <div style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
                                    <div style="justify-content: flex-start; align-items: flex-start; display: inline-flex">
                                        <div style="justify-content: center; display: flex; flex-direction: column; color: #F8F9FA; font-size: 14.50px; font-family: Inter; font-weight: 700; line-height: 24px; word-wrap: break-word">Courses</div>
                                    </div>
                                </div>
                                <div style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
                                    <div style="justify-content: flex-start; align-items: flex-start; display: inline-flex">
                                        <div style="justify-content: center; display: flex; flex-direction: column; color: #F8F9FA; font-size: 15.12px; font-family: Inter; font-weight: 700; line-height: 24px; word-wrap: break-word">Corporate Training</div>
                                    </div>
                                </div>
                                <div style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
                                    <div style="justify-content: flex-start; align-items: flex-start; display: inline-flex">
                                        <div style="justify-content: center; display: flex; flex-direction: column; color: #F8F9FA; font-size: 14.75px; font-family: Inter; font-weight: 700; line-height: 24px; word-wrap: break-word">Contact</div>
                                    </div>
                                </div>
                                <div style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
                                    <div style="justify-content: flex-start; align-items: flex-start; display: inline-flex">
                                        <div style="justify-content: center; display: flex; flex-direction: column; color: #F8F9FA; font-size: 15px; font-family: Inter; font-weight: 700; line-height: 24px; word-wrap: break-word">Calendar</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="flex: 1 1 0; height: 163.18px; max-width: 1320px; padding-left: 12px; padding-right: 12px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                        <div style="align-self: stretch; height: 27.19px; position: relative">
                            <div style="width: 285px; padding-bottom: 0.59px; left: 0px; top: -0.60px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                                <div style="justify-content: center; display: flex; flex-direction: column; color: white; font-size: 14.75px; font-family: Inter; font-weight: 700; line-height: 19.20px; word-wrap: break-word">AFP MEA</div>
                            </div>
                        </div>
                        <div style="align-self: stretch; padding-bottom: 16px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
                            <div style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
                                <div style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
                                    <div style="justify-content: flex-start; align-items: flex-start; display: inline-flex">
                                        <div style="justify-content: center; display: flex; flex-direction: column; color: #F8F9FA; font-size: 15.62px; font-family: Inter; font-weight: 700; line-height: 24px; word-wrap: break-word">AFP MEA</div>
                                    </div>
                                </div>
                                <div style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
                                    <div style="justify-content: flex-start; align-items: flex-start; display: inline-flex">
                                        <div style="justify-content: center; display: flex; flex-direction: column; color: #F8F9FA; font-size: 14.38px; font-family: Inter; font-weight: 700; line-height: 24px; word-wrap: break-word">CTP</div>
                                    </div>
                                </div>
                                <div style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
                                    <div style="justify-content: flex-start; align-items: flex-start; display: inline-flex">
                                        <div style="justify-content: center; display: flex; flex-direction: column; color: #F8F9FA; font-size: 15.38px; font-family: Inter; font-weight: 700; line-height: 24px; word-wrap: break-word">FPAC</div>
                                    </div>
                                </div>
                                <div style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
                                    <div style="justify-content: flex-start; align-items: flex-start; display: inline-flex">
                                        <div style="justify-content: center; display: flex; flex-direction: column; color: #F8F9FA; font-size: 14.88px; font-family: Inter; font-weight: 700; line-height: 24px; word-wrap: break-word">In-Person RoundTables</div>
                                    </div>
                                </div>
                                <div style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
                                    <div style="justify-content: flex-start; align-items: flex-start; display: inline-flex">
                                        <div style="justify-content: center; display: flex; flex-direction: column; color: #F8F9FA; font-size: 15.25px; font-family: Inter; font-weight: 700; line-height: 24px; word-wrap: break-word">AFP Membership</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="flex: 1 1 0; height: 163.18px; max-width: 1320px; padding-left: 12px; padding-right: 12px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                        <div style="align-self: stretch; height: 27.19px; position: relative">
                            <div style="width: 285px; padding-bottom: 0.59px; left: 0px; top: -0.60px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                                <div style="justify-content: center; display: flex; flex-direction: column; color: white; font-size: 14.25px; font-family: Inter; font-weight: 700; line-height: 19.20px; word-wrap: break-word">More</div>
                            </div>
                        </div>
                        <div style="align-self: stretch; padding-bottom: 16px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
                            <div style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
                                <div style="justify-content: flex-start; align-items: flex-start; display: inline-flex">
                                    <div style="justify-content: center; display: flex; flex-direction: column; color: #F8F9FA; font-size: 14.62px; font-family: Inter; font-weight: 700; line-height: 24px; word-wrap: break-word">Careers</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="width: 138px; height: 44px; max-width: 1320px; padding-left: 12px; padding-right: 12px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                        <div style="justify-content: flex-start; align-items: center; display: inline-flex">
                            <img style="width: 117.50px; height: 45.08px; position: relative" src="https://placehold.co/117x45" />
                        </div>
                    </div>
                </div>
                <div style="align-self: stretch; height: 1px; position: relative; opacity: 0.25; border-top: 1px white solid"></div>
                <div style="align-self: stretch; padding-top: 16px; justify-content: space-between; align-items: center; display: inline-flex">
                    <div style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                        <div style="justify-content: center; display: flex; flex-direction: column; color: white; font-size: 14.88px; font-family: Inter; font-weight: 700; line-height: 24px; word-wrap: break-word">Copyright © 2024 Beacon FinTrain</div>
                    </div>
                    <div style="padding-top: 4px; padding-bottom: 3.17px; justify-content: flex-start; align-items: flex-start; gap: 12.10px; display: flex">
                        <div style="padding-top: 0.83px; justify-content: flex-start; align-items: center; display: flex">
                            <div style="width: 16px; height: 16px; position: relative">
                                <div style="width: 16px; height: 16px; left: 0px; top: 0px; position: absolute; background: white"></div>
                            </div>
                        </div>
                        <div style="padding-top: 0.83px; justify-content: flex-start; align-items: center; display: flex">
                            <div style="width: 16px; height: 16px; position: relative">
                                <div style="width: 16px; height: 11.24px; left: 0px; top: 2px; position: absolute; background: white"></div>
                            </div>
                        </div>
                        <div style="padding-top: 0.83px; justify-content: flex-start; align-items: center; display: flex">
                            <div style="width: 16px; height: 16px; position: relative">
                                <div style="width: 16px; height: 16px; left: 0px; top: 0px; position: absolute; background: white"></div>
                            </div>
                        </div>
                        <div style="padding-top: 0.83px; justify-content: flex-start; align-items: center; display: flex">
                            <div style="width: 16px; height: 16px; position: relative">
                                <div style="width: 16px; height: 16px; left: 0px; top: 0px; position: absolute; background: white"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
