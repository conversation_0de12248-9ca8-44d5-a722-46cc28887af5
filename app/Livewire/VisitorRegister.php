<?php

namespace App\Livewire;

use App\Services\CountryService;
use App\Services\VisitorRegistrationService;
use App\Traits\WithCountryHandling;
use App\ValueObjects\VisitorFormData;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\View;
use Illuminate\Validation\ValidationException;
use JetBrains\PhpStorm\NoReturn;
use Livewire\Component;
use Livewire\WithFileUploads;

class VisitorRegister extends Component
{
    use WithFileUploads, WithCountryHandling;

    // Basic properties
    public $currentStep = 1;
    public $totalSteps = 3;

    // Step 1: Basic Information
    public $firstName;
    public $lastName;
    public $nationality;
    public $email;
    public $phone;
    public $whatsappNumber;
    public $password;
    public $passwordConfirmation;
    public $profilePhoto;
    public $profilePhotoData; // Added to store the base64 image data
    public $photoConsent = false;

    // Step 2: Company Information
    public $companyName;
    public $jobTitle;
    public $jobTitle2;
    public $linkedin;
    public $industry;
    public $discountCode;

    // Step 3: Workshop Selection
    public $workshops;
    public $code;
    public $isValidCode = true;
    public $errorMessage = '';

    // Service instance
    protected $registrationService;

    /**
     * Rules for validation
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            'firstName' => 'required|min:2|max:50',
            'lastName' => 'required|min:2|max:50',
            'nationality' => 'required',
            'country' => 'required',
            'email' => 'required|email|unique:visitors,email',
            'phone' => 'required|regex:/^[0-9]{10,12}$/',
            'password' => 'required|min:8|same:passwordConfirmation',
            'passwordConfirmation' => 'required',
            'profilePhoto' => 'nullable|image|max:1024',
            'photoConsent' => 'required_with:profilePhoto|accepted',
            'companyName' => 'required|min:2|max:100',
            'jobTitle' => 'required|min:2|max:100',
            'jobTitle2' => 'nullable|max:100',
            'linkedin' => 'nullable|url|max:255',
            'industry' => 'required',
        ];
    }

    /**
     * Custom validation messages
     *
     * @return array
     */
    protected function messages(): array
    {
        return [
            'firstName.required' => 'First name is required',
            'lastName.required' => 'Last name is required',
            'nationality.required' => 'Please select your nationality',
            'email.required' => 'Email address is required',
            'email.email' => 'Please enter a valid email address',
            'email.unique' => 'This email is already registered',
            'phone.required' => 'Phone number is required',
            'phone.regex' => 'Please enter a valid phone number',
            'password.required' => 'Password is required',
            'password.min' => 'Password must be at least 8 characters',
            'password.same' => 'Passwords do not match',
            'profilePhoto.image' => 'The file must be an image',
            'profilePhoto.max' => 'The image must not exceed 1MB',
            'photoConsent.required_with' => 'You must agree to the photo consent terms',
            'photoConsent.accepted' => 'You must agree to the photo consent terms',
        ];
    }

    public function boot(): void
    {
        $this->registrationService = app(VisitorRegistrationService::class);
    }

    #[NoReturn]
    public function mount(): void
    {
        $this->code = request()->query('code');
        $this->workshops = $this->registrationService->getWorkshops(1);
        $this->validateCode();
        $this->loadCountries();
        $this->initializeCountry();
    }

    /**
     * Validate registration code
     */
    private function validateCode(): void
    {
        if ($this->code) {
            $isValid = $this->registrationService->validateCode($this->code);
            if (!$isValid) {
                $this->isValidCode = false;
                $this->errorMessage = 'This code has expired. Please contact your HR.';
                session()->flash('error', $this->errorMessage);
            }
        }
    }

    /**
     * Handle file upload validation
     */
    public function updatedProfilePhoto(): void
    {
        $this->validateOnly('profilePhoto');
    }

    /**
     * Real-time validation
     * @throws ValidationException
     */
    public function updated($propertyName): void
    {
        $this->validateOnly($propertyName);
    }

    /**
     * Get validation rules for a specific step
     */
    protected function getStepRules(int $step): array
    {
        return match ($step) {
            1 => [
                'firstName' => 'required|min:2|max:50',
                'lastName' => 'required|min:2|max:50',
                'nationality' => 'required',
                'country' => 'required',
                'email' => 'required|email|unique:visitors,email',
                'phone' => 'required|regex:/^[0-9]{10,12}$/',
                'password' => 'required|min:8|same:passwordConfirmation',
                'passwordConfirmation' => 'required',
                'profilePhoto' => 'nullable|image|max:1024',
                'photoConsent' => 'required_with:profilePhoto|accepted',
            ],
            2 => [
                'companyName' => 'required|min:2|max:100',
                'jobTitle' => 'required|min:2|max:100',
                'jobTitle2' => 'nullable|max:100',
                'linkedin' => 'nullable|url|max:255',
                'industry' => 'required',
            ],
            default => [],
        };
    }

    /**
     * Get validation messages
     */
    protected function getValidationMessages(): array
    {
        return $this->messages();
    }

    /**
     * Move to next step
     */
    public function nextStep(): void
    {
        $rules = $this->getStepRules($this->currentStep);
        $this->validate($rules, $this->getValidationMessages());

        if ($this->currentStep < $this->totalSteps) {
            $this->currentStep++;
        }
    }

    /**
     * Go back to previous step
     */
    public function previousStep(): void
    {
        if ($this->currentStep > 1) {
            $this->currentStep--;
        }
    }

    /**
     * Register the visitor
     */
    public function register(): void
    {
        try {
            // Create form data value object
            $formData = VisitorFormData::fromLivewire($this);

            // Register visitor using service (without sending verification email yet)
            $visitor = $this->registrationService->registerWithoutVerification($formData, $this->profilePhoto);

            // Log the visitor in automatically
            Auth::guard('visitor')->login($visitor);

            // Now send the verification email
            $this->registrationService->sendVerificationEmail($visitor);

            // Flash success message
            session()->flash('success', 'Registration successful! A verification email has been sent to your email address.');

            // Redirect to ticket page
            $this->redirect(route('visitor.ticket'));
        } catch (\Exception $e) {
            session()->flash('error', 'Registration failed: ' . $e->getMessage());
        }
    }

    /**
     * Render the component
     */
    public function render(): \Illuminate\View\View
    {
        return view('livewire.visitor-register');
    }
}
