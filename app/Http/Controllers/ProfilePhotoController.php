<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ProfilePhotoController extends Controller
{
    /**
     * Handle profile photo upload
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function upload(Request $request)
    {
        try {
            // Validate the request
            $request->validate([
                'profile_photo' => 'required|image|max:1024', // 1MB max
            ]);

            // Get the file from the request
            $file = $request->file('profile_photo');
            
            // Generate a unique filename
            $filename = 'profile_' . Str::random(20) . '.' . $file->getClientOriginalExtension();
            
            // Store the file
            $path = $file->storeAs('profile-photos', $filename, 'public');
            
            // Store the file path in the session so Livewire can access it
            session()->put('profile_photo_path', $path);
            session()->put('profile_photo_uploaded', true);
            
            return response()->json([
                'success' => true,
                'path' => $path,
                'url' => Storage::url($path),
                'message' => 'Profile photo uploaded successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload profile photo: ' . $e->getMessage()
            ], 500);
        }
    }
}
